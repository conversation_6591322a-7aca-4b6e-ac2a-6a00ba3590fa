export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      business_target_categories: {
        Row: {
          business_id: string
          category_id: number
          created_at: string | null
        }
        Insert: {
          business_id: string
          category_id: number
          created_at?: string | null
        }
        Update: {
          business_id?: string
          category_id?: number
          created_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "business_target_categories_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "business_target_categories_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      businesses: {
        Row: {
          budget_range: string | null
          company_name: string
          company_size: string | null
          created_at: string | null
          id: string
          industry: string | null
          is_verified: boolean | null
          updated_at: string | null
        }
        Insert: {
          budget_range?: string | null
          company_name: string
          company_size?: string | null
          created_at?: string | null
          id: string
          industry?: string | null
          is_verified?: boolean | null
          updated_at?: string | null
        }
        Update: {
          budget_range?: string | null
          company_name?: string
          company_size?: string | null
          created_at?: string | null
          id?: string
          industry?: string | null
          is_verified?: boolean | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "businesses_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_applications: {
        Row: {
          additional_services: string | null
          applied_at: string | null
          audience_insights: string | null
          available_start_date: string | null
          campaign_id: string
          delivery_timeframe: string | null
          experience_relevant: string | null
          id: string
          influencer_id: string
          portfolio_links: string[] | null
          proposal_text: string | null
          proposed_rate: number
          responded_at: string | null
          status: Database["public"]["Enums"]["application_status"] | null
        }
        Insert: {
          additional_services?: string | null
          applied_at?: string | null
          audience_insights?: string | null
          available_start_date?: string | null
          campaign_id: string
          delivery_timeframe?: string | null
          experience_relevant?: string | null
          id?: string
          influencer_id: string
          portfolio_links?: string[] | null
          proposal_text?: string | null
          proposed_rate: number
          responded_at?: string | null
          status?: Database["public"]["Enums"]["application_status"] | null
        }
        Update: {
          additional_services?: string | null
          applied_at?: string | null
          audience_insights?: string | null
          available_start_date?: string | null
          campaign_id?: string
          delivery_timeframe?: string | null
          experience_relevant?: string | null
          id?: string
          influencer_id?: string
          portfolio_links?: string[] | null
          proposal_text?: string | null
          proposed_rate?: number
          responded_at?: string | null
          status?: Database["public"]["Enums"]["application_status"] | null
        }
        Relationships: [
          {
            foreignKeyName: "campaign_applications_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_applications_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencer_search_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_applications_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencers"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_categories: {
        Row: {
          campaign_id: string
          category_id: number
          created_at: string | null
        }
        Insert: {
          campaign_id: string
          category_id: number
          created_at?: string | null
        }
        Update: {
          campaign_id?: string
          category_id?: number
          created_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "campaign_categories_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_categories_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_content_types: {
        Row: {
          campaign_id: string
          content_type_id: number
          created_at: string | null
        }
        Insert: {
          campaign_id: string
          content_type_id: number
          created_at?: string | null
        }
        Update: {
          campaign_id?: string
          content_type_id?: number
          created_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "campaign_content_types_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_content_types_content_type_id_fkey"
            columns: ["content_type_id"]
            isOneToOne: false
            referencedRelation: "content_types"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_platforms: {
        Row: {
          campaign_id: string
          created_at: string | null
          platform_id: number
        }
        Insert: {
          campaign_id: string
          created_at?: string | null
          platform_id: number
        }
        Update: {
          campaign_id?: string
          created_at?: string | null
          platform_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "campaign_platforms_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_platforms_platform_id_fkey"
            columns: ["platform_id"]
            isOneToOne: false
            referencedRelation: "platforms"
            referencedColumns: ["id"]
          },
        ]
      }
      campaigns: {
        Row: {
          budget: number
          business_id: string
          campaign_type: string | null
          content_guidelines: string | null
          created_at: string | null
          description: string
          end_date: string
          id: string
          is_active: boolean | null
          max_applications: number | null
          payment_terms: string | null
          requirements: string | null
          start_date: string
          status: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          budget: number
          business_id: string
          campaign_type?: string | null
          content_guidelines?: string | null
          created_at?: string | null
          description: string
          end_date: string
          id?: string
          is_active?: boolean | null
          max_applications?: number | null
          payment_terms?: string | null
          requirements?: string | null
          start_date: string
          status?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          budget?: number
          business_id?: string
          campaign_type?: string | null
          content_guidelines?: string | null
          created_at?: string | null
          description?: string
          end_date?: string
          id?: string
          is_active?: boolean | null
          max_applications?: number | null
          payment_terms?: string | null
          requirements?: string | null
          start_date?: string
          status?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "campaigns_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
        ]
      }
      categories: {
        Row: {
          created_at: string | null
          description: string | null
          icon: string | null
          id: number
          name: string
          slug: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: number
          name: string
          slug: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: number
          name?: string
          slug?: string
        }
        Relationships: []
      }
      chat_messages: {
        Row: {
          content: string
          created_at: string | null
          id: string
          is_read: boolean | null
          message_type: string | null
          receiver_id: string
          sender_id: string
          thread_id: string | null
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          is_read?: boolean | null
          message_type?: string | null
          receiver_id: string
          sender_id: string
          thread_id?: string | null
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          is_read?: boolean | null
          message_type?: string | null
          receiver_id?: string
          sender_id?: string
          thread_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "chat_messages_receiver_id_fkey"
            columns: ["receiver_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      content_types: {
        Row: {
          created_at: string | null
          description: string | null
          id: number
          is_active: boolean | null
          name: string
          platform_id: number | null
          slug: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: number
          is_active?: boolean | null
          name: string
          platform_id?: number | null
          slug: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: number
          is_active?: boolean | null
          name?: string
          platform_id?: number | null
          slug?: string
        }
        Relationships: [
          {
            foreignKeyName: "content_types_platform_id_fkey"
            columns: ["platform_id"]
            isOneToOne: false
            referencedRelation: "platforms"
            referencedColumns: ["id"]
          },
        ]
      }
      direct_offers: {
        Row: {
          business_id: string
          content_guidelines: string | null
          created_at: string | null
          description: string
          id: string
          influencer_id: string
          payment_amount: number
          payment_terms: string | null
          requirements: string | null
          responded_at: string | null
          status: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          business_id: string
          content_guidelines?: string | null
          created_at?: string | null
          description: string
          id?: string
          influencer_id: string
          payment_amount: number
          payment_terms?: string | null
          requirements?: string | null
          responded_at?: string | null
          status?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          business_id?: string
          content_guidelines?: string | null
          created_at?: string | null
          description?: string
          id?: string
          influencer_id?: string
          payment_amount?: number
          payment_terms?: string | null
          requirements?: string | null
          responded_at?: string | null
          status?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "direct_offers_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "direct_offers_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencer_search_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "direct_offers_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencers"
            referencedColumns: ["id"]
          },
        ]
      }
      influencer_categories: {
        Row: {
          category_id: number
          created_at: string | null
          influencer_id: string
          is_primary: boolean | null
        }
        Insert: {
          category_id: number
          created_at?: string | null
          influencer_id: string
          is_primary?: boolean | null
        }
        Update: {
          category_id?: number
          created_at?: string | null
          influencer_id?: string
          is_primary?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "influencer_categories_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "influencer_categories_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencer_search_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "influencer_categories_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencers"
            referencedColumns: ["id"]
          },
        ]
      }
      influencer_platform_pricing: {
        Row: {
          auto_generated_name: string | null
          content_type_id: number | null
          created_at: string | null
          currency: string | null
          id: number
          influencer_id: string | null
          is_available: boolean | null
          platform_id: number | null
          price: number
          quantity: number | null
          updated_at: string | null
          video_duration: string | null
        }
        Insert: {
          auto_generated_name?: string | null
          content_type_id?: number | null
          created_at?: string | null
          currency?: string | null
          id?: number
          influencer_id?: string | null
          is_available?: boolean | null
          platform_id?: number | null
          price: number
          quantity?: number | null
          updated_at?: string | null
          video_duration?: string | null
        }
        Update: {
          auto_generated_name?: string | null
          content_type_id?: number | null
          created_at?: string | null
          currency?: string | null
          id?: number
          influencer_id?: string | null
          is_available?: boolean | null
          platform_id?: number | null
          price?: number
          quantity?: number | null
          updated_at?: string | null
          video_duration?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "influencer_platform_pricing_content_type_id_fkey"
            columns: ["content_type_id"]
            isOneToOne: false
            referencedRelation: "content_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "influencer_platform_pricing_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencer_search_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "influencer_platform_pricing_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "influencer_platform_pricing_platform_id_fkey"
            columns: ["platform_id"]
            isOneToOne: false
            referencedRelation: "platforms"
            referencedColumns: ["id"]
          },
        ]
      }
      influencer_platforms: {
        Row: {
          created_at: string | null
          followers_count: number | null
          handle: string | null
          influencer_id: string
          is_active: boolean | null
          is_verified: boolean | null
          platform_id: number
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          followers_count?: number | null
          handle?: string | null
          influencer_id: string
          is_active?: boolean | null
          is_verified?: boolean | null
          platform_id: number
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          followers_count?: number | null
          handle?: string | null
          influencer_id?: string
          is_active?: boolean | null
          is_verified?: boolean | null
          platform_id?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "influencer_platforms_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencer_search_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "influencer_platforms_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "influencer_platforms_platform_id_fkey"
            columns: ["platform_id"]
            isOneToOne: false
            referencedRelation: "platforms"
            referencedColumns: ["id"]
          },
        ]
      }
      influencers: {
        Row: {
          age: number | null
          created_at: string | null
          engagement_rate: number | null
          gender: string | null
          id: string
          instagram_followers: number | null
          instagram_handle: string | null
          is_verified: boolean | null
          portfolio_urls: string[] | null
          price_per_post: number | null
          price_per_reel: number | null
          price_per_story: number | null
          tiktok_followers: number | null
          tiktok_handle: string | null
          updated_at: string | null
          youtube_handle: string | null
          youtube_subscribers: number | null
        }
        Insert: {
          age?: number | null
          created_at?: string | null
          engagement_rate?: number | null
          gender?: string | null
          id: string
          instagram_followers?: number | null
          instagram_handle?: string | null
          is_verified?: boolean | null
          portfolio_urls?: string[] | null
          price_per_post?: number | null
          price_per_reel?: number | null
          price_per_story?: number | null
          tiktok_followers?: number | null
          tiktok_handle?: string | null
          updated_at?: string | null
          youtube_handle?: string | null
          youtube_subscribers?: number | null
        }
        Update: {
          age?: number | null
          created_at?: string | null
          engagement_rate?: number | null
          gender?: string | null
          id?: string
          instagram_followers?: number | null
          instagram_handle?: string | null
          is_verified?: boolean | null
          portfolio_urls?: string[] | null
          price_per_post?: number | null
          price_per_reel?: number | null
          price_per_story?: number | null
          tiktok_followers?: number | null
          tiktok_handle?: string | null
          updated_at?: string | null
          youtube_handle?: string | null
          youtube_subscribers?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "influencers_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      job_completions: {
        Row: {
          campaign_id: string | null
          completed_at: string | null
          content_urls: string[] | null
          created_at: string | null
          direct_offer_id: string | null
          id: string
          influencer_id: string
          notes: string | null
          status: string | null
          submitted_at: string | null
          updated_at: string | null
        }
        Insert: {
          campaign_id?: string | null
          completed_at?: string | null
          content_urls?: string[] | null
          created_at?: string | null
          direct_offer_id?: string | null
          id?: string
          influencer_id: string
          notes?: string | null
          status?: string | null
          submitted_at?: string | null
          updated_at?: string | null
        }
        Update: {
          campaign_id?: string | null
          completed_at?: string | null
          content_urls?: string[] | null
          created_at?: string | null
          direct_offer_id?: string | null
          id?: string
          influencer_id?: string
          notes?: string | null
          status?: string | null
          submitted_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "job_completions_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "job_completions_direct_offer_id_fkey"
            columns: ["direct_offer_id"]
            isOneToOne: false
            referencedRelation: "direct_offers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "job_completions_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencer_search_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "job_completions_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencers"
            referencedColumns: ["id"]
          },
        ]
      }
      job_reviews: {
        Row: {
          business_id: string
          campaign_id: string | null
          created_at: string | null
          direct_offer_id: string | null
          id: string
          influencer_id: string
          job_completion_id: string
          rating: number
          review_text: string | null
          updated_at: string | null
        }
        Insert: {
          business_id: string
          campaign_id?: string | null
          created_at?: string | null
          direct_offer_id?: string | null
          id?: string
          influencer_id: string
          job_completion_id: string
          rating: number
          review_text?: string | null
          updated_at?: string | null
        }
        Update: {
          business_id?: string
          campaign_id?: string | null
          created_at?: string | null
          direct_offer_id?: string | null
          id?: string
          influencer_id?: string
          job_completion_id?: string
          rating?: number
          review_text?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "job_reviews_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "job_reviews_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "job_reviews_direct_offer_id_fkey"
            columns: ["direct_offer_id"]
            isOneToOne: false
            referencedRelation: "direct_offers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "job_reviews_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencer_search_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "job_reviews_influencer_id_fkey"
            columns: ["influencer_id"]
            isOneToOne: false
            referencedRelation: "influencers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "job_reviews_job_completion_id_fkey"
            columns: ["job_completion_id"]
            isOneToOne: false
            referencedRelation: "job_completions"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          created_at: string | null
          id: string
          is_read: boolean | null
          message: string
          title: string
          type: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_read?: boolean | null
          message: string
          title: string
          type?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          is_read?: boolean | null
          message?: string
          title?: string
          type?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      platforms: {
        Row: {
          created_at: string | null
          icon: string | null
          id: number
          is_active: boolean | null
          name: string
          slug: string
        }
        Insert: {
          created_at?: string | null
          icon?: string | null
          id?: number
          is_active?: boolean | null
          name: string
          slug: string
        }
        Update: {
          created_at?: string | null
          icon?: string | null
          id?: number
          is_active?: boolean | null
          name?: string
          slug?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          address: string | null
          age: number | null
          avatar_url: string | null
          navbar_avatar_url: string | null
          card_avatar_url: string | null
          profile_avatar_url: string | null
          preview_avatar_url: string | null
          average_rating: number | null
          bank_account: string | null
          bank_name: string | null
          bio: string | null
          city: string | null
          country: string | null
          created_at: string | null
          full_name: string | null
          gender: string | null
          id: string
          location: string | null
          phone: string | null
          postal_code: string | null
          profile_completed: boolean | null
          tax_id: string | null
          total_reviews: number | null
          updated_at: string | null
          user_type: Database["public"]["Enums"]["user_type"]
          username: string | null
          website_url: string | null
        }
        Insert: {
          address?: string | null
          age?: number | null
          avatar_url?: string | null
          navbar_avatar_url?: string | null
          card_avatar_url?: string | null
          profile_avatar_url?: string | null
          preview_avatar_url?: string | null
          average_rating?: number | null
          bank_account?: string | null
          bank_name?: string | null
          bio?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          full_name?: string | null
          gender?: string | null
          id: string
          location?: string | null
          phone?: string | null
          postal_code?: string | null
          profile_completed?: boolean | null
          tax_id?: string | null
          total_reviews?: number | null
          updated_at?: string | null
          user_type: Database["public"]["Enums"]["user_type"]
          username?: string | null
          website_url?: string | null
        }
        Update: {
          address?: string | null
          age?: number | null
          avatar_url?: string | null
          navbar_avatar_url?: string | null
          card_avatar_url?: string | null
          profile_avatar_url?: string | null
          preview_avatar_url?: string | null
          average_rating?: number | null
          bank_account?: string | null
          bank_name?: string | null
          bio?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          full_name?: string | null
          gender?: string | null
          id?: string
          location?: string | null
          phone?: string | null
          postal_code?: string | null
          profile_completed?: boolean | null
          tax_id?: string | null
          total_reviews?: number | null
          updated_at?: string | null
          user_type?: Database["public"]["Enums"]["user_type"]
          username?: string | null
          website_url?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      influencer_search_view: {
        Row: {
          age: number | null
          average_rating: number | null
          bio: string | null
          categories: string[] | null
          city: string | null
          country: string | null
          created_at: string | null
          engagement_rate: number | null
          full_name: string | null
          gender: string | null
          id: string | null
          instagram_followers: number | null
          instagram_handle: string | null
          is_verified: boolean | null
          platforms: string[] | null
          portfolio_urls: string[] | null
          price_per_post: number | null
          price_per_reel: number | null
          price_per_story: number | null
          tiktok_followers: number | null
          tiktok_handle: string | null
          total_reviews: number | null
          username: string | null
          youtube_handle: string | null
          youtube_subscribers: number | null
        }
        Relationships: []
      }
    }
    Functions: {
      create_notification: {
        Args: {
          p_user_id: string
          p_title: string
          p_message: string
          p_type?: string
        }
        Returns: string
      }
    }
    Enums: {
      application_status: "pending" | "accepted" | "rejected" | "completed"
      user_type: "influencer" | "business"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}