'use client';

import { MobileBottomNavigation } from './MobileBottomNavigation';
import { MobileTopNavbar } from './MobileTopNavbar';

interface ResponsiveNavigationProps {
  userType: 'influencer' | 'business';
  profile: any;
}

export function ResponsiveNavigation({ userType, profile }: ResponsiveNavigationProps) {
  return (
    <>
      {/* Mobile Top Navbar - prikazuje se samo na mobile uređajima */}
      <MobileTopNavbar userType={userType} profile={profile} />

      {/* Mobile Bottom Navigation - prikazuje se samo na mobile uređajima */}
      <MobileBottomNavigation userType={userType} />
    </>
  );
}
