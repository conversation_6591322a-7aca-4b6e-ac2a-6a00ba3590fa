import imageCompression from 'browser-image-compression';

// Image size configurations for different use cases
export const IMAGE_SIZES = {
  navbar: { width: 40, height: 40, quality: 0.95 },
  card: { width: 800, height: 800, quality: 0.95 }, // Increased size and quality for better marketplace display
  profile: { width: 800, height: 800, quality: 0.95 },
  preview: { width: 800, height: 800, quality: 0.95 },
} as const;

export type ImageSizeType = keyof typeof IMAGE_SIZES;

// Compression options
const DEFAULT_COMPRESSION_OPTIONS = {
  maxSizeMB: 1, // Maximum file size in MB
  useWebWorker: true, // Use web worker for better performance
  fileType: 'image/webp' as const, // Convert to WebP format
  initialQuality: 0.9,
};

/**
 * Compress and resize image to multiple sizes
 */
export async function compressImageToMultipleSizes(
  file: File
): Promise<{
  navbar: File;
  card: File;
  profile: File;
  preview: File;
}> {
  const results = await Promise.all([
    compressImageToSize(file, 'navbar'),
    compressImageToSize(file, 'card'),
    compressImageToSize(file, 'profile'),
    compressImageToSize(file, 'preview'),
  ]);

  return {
    navbar: results[0],
    card: results[1],
    profile: results[2],
    preview: results[3],
  };
}

/**
 * Compress image to specific size
 */
export async function compressImageToSize(
  file: File,
  sizeType: ImageSizeType
): Promise<File> {
  const config = IMAGE_SIZES[sizeType];
  
  const options = {
    ...DEFAULT_COMPRESSION_OPTIONS,
    maxWidthOrHeight: Math.max(config.width, config.height),
    initialQuality: config.quality,
  };

  try {
    const compressedFile = await imageCompression(file, options);
    
    // Create a new file with proper naming
    const fileName = `${sizeType}-${Date.now()}.webp`;
    return new File([compressedFile], fileName, {
      type: 'image/webp',
      lastModified: Date.now(),
    });
  } catch (error) {
    console.error(`Error compressing image for ${sizeType}:`, error);
    throw new Error(`Failed to compress image for ${sizeType}`);
  }
}

/**
 * Validate image file before processing
 */
export function validateImageFile(file: File): {
  isValid: boolean;
  error?: string;
} {
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Dozvoljen je samo JPEG, PNG ili WebP format.',
    };
  }

  // Check file size (max 10MB)
  const maxSizeInBytes = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSizeInBytes) {
    return {
      isValid: false,
      error: 'Slika ne smije biti veća od 10MB.',
    };
  }

  return { isValid: true };
}

/**
 * Create image preview URL
 */
export function createImagePreview(file: File): string {
  return URL.createObjectURL(file);
}

/**
 * Clean up image preview URL
 */
export function cleanupImagePreview(url: string): void {
  URL.revokeObjectURL(url);
}

/**
 * Get file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Calculate compression ratio
 */
export function calculateCompressionRatio(
  originalSize: number,
  compressedSize: number
): number {
  return Math.round(((originalSize - compressedSize) / originalSize) * 100);
}

/**
 * Progress callback type for compression
 */
export type CompressionProgressCallback = (progress: {
  stage: 'validating' | 'compressing' | 'uploading' | 'complete';
  percentage: number;
  currentSize?: ImageSizeType;
}) => void;

/**
 * Compress image with progress tracking
 */
export async function compressImageWithProgress(
  file: File,
  onProgress?: CompressionProgressCallback
): Promise<{
  navbar: File;
  card: File;
  profile: File;
  preview: File;
  originalSize: number;
  totalCompressedSize: number;
  compressionRatio: number;
}> {
  const originalSize = file.size;
  
  // Validation stage
  onProgress?.({ stage: 'validating', percentage: 0 });
  
  const validation = validateImageFile(file);
  if (!validation.isValid) {
    throw new Error(validation.error);
  }

  // Compression stage
  onProgress?.({ stage: 'compressing', percentage: 10 });

  const sizeTypes: ImageSizeType[] = ['navbar', 'card', 'profile', 'preview'];
  const compressedFiles: Record<ImageSizeType, File> = {} as any;
  
  for (let i = 0; i < sizeTypes.length; i++) {
    const sizeType = sizeTypes[i];
    onProgress?.({
      stage: 'compressing',
      percentage: 10 + (i * 20),
      currentSize: sizeType,
    });
    
    compressedFiles[sizeType] = await compressImageToSize(file, sizeType);
  }

  const totalCompressedSize = Object.values(compressedFiles).reduce(
    (total, file) => total + file.size,
    0
  );

  const compressionRatio = calculateCompressionRatio(originalSize, totalCompressedSize);

  onProgress?.({ stage: 'complete', percentage: 100 });

  return {
    navbar: compressedFiles.navbar,
    card: compressedFiles.card,
    profile: compressedFiles.profile,
    preview: compressedFiles.preview,
    originalSize,
    totalCompressedSize,
    compressionRatio,
  };
}
